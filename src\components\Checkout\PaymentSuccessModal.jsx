import React from 'react';
import { FiCheckCircle, FiX } from 'react-icons/fi';
import './Checkout.css';

const PaymentSuccessModal = ({ isOpen, onClose, orderInfo }) => {
  if (!isOpen) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content payment-success-modal">
        <button className="modal-close" onClick={onClose}>
          <FiX />
        </button>
        
        <div className="success-icon">
          <FiCheckCircle />
        </div>
        
        <h2 className="success-title">Thanh toán thành công!</h2>
        
        <div className="success-message">
          <p>Cảm ơn bạn đã đặt hàng. Đơn hàng của bạn đã được xác nhận và đang được xử lý.</p>
        </div>
        
        <div className="order-details">
          <div className="detail-row">
            <span>Mã đơn hàng:</span>
            <span className="order-id">#{orderInfo?.orderId || 'DH001234'}</span>
          </div>
          <div className="detail-row">
            <span>Tổng tiền:</span>
            <span className="total-amount">{orderInfo?.total?.toLocaleString() || '360,000'}đ</span>
          </div>
          <div className="detail-row">
            <span>Phương thức thanh toán:</span>
            <span>{orderInfo?.paymentMethod || 'Thẻ tín dụng'}</span>
          </div>
          <div className="detail-row">
            <span>Thời gian giao hàng dự kiến:</span>
            <span>3-5 ngày làm việc</span>
          </div>
        </div>
        
        <div className="modal-actions">
          <button className="btn-primary" onClick={onClose}>
            Tiếp tục mua sắm
          </button>
          <button className="btn-secondary">
            Xem đơn hàng
          </button>
        </div>
      </div>
    </div>
  );
};

export default PaymentSuccessModal;
