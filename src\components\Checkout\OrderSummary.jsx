import React, { useState } from 'react';
import PaymentSuccessModal from './PaymentSuccessModal';
import './Checkout.css';

const OrderSummary = () => {
  // Sample order data
  const orderItems = [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON> cổ Mặt <PERSON>ình <PERSON>',
      description: 'Bằng Gố<PERSON> Đ<PERSON>ển <PERSON>',
      price: 80000,
      quantity: 2,
      total: 160000
    },
    {
      id: 2,
      name: '<PERSON>òng Ta<PERSON> Trang Sức Bãi Biển Quà',
      description: 'Thời Trang Handmade Boho Vỏ Ốc Sao Biển Quyến Rũ Đ<PERSON>',
      price: 50000,
      quantity: 4,
      total: 200000
    }
  ];

  // Calculate totals
  const subtotal = orderItems.reduce((sum, item) => sum + item.total, 0);
  const shipping = 0;
  const discount = 0;
  const total = subtotal + shipping - discount;
  
  const [discountCode, setDiscountCode] = useState('');
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  
  const handleDiscountChange = (e) => {
    setDiscountCode(e.target.value);
  };
  
  const handleTermsChange = (e) => {
    setTermsAccepted(e.target.checked);
  };
  
  const applyDiscount = () => {
    // Discount application logic would go here
    console.log('Applying discount:', discountCode);
  };

  const handlePayment = async () => {
    if (!termsAccepted) {
      alert('Vui lòng đồng ý với các điều khoản chính sách giao hàng');
      return;
    }

    setIsProcessingPayment(true);

    // Simulate payment processing
    try {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      setShowSuccessModal(true);
    } catch (error) {
      alert('Có lỗi xảy ra trong quá trình thanh toán. Vui lòng thử lại.');
    } finally {
      setIsProcessingPayment(false);
    }
  };

  const closeSuccessModal = () => {
    setShowSuccessModal(false);
  };

  return (
    <div className="checkout-section order-summary">
      <h2 className="section-title">4. Thông tin giỏ hàng</h2>
      
      <div className="order-items">
        <div className="order-header">
          <div className="product-name">Tên sản phẩm</div>
          <div className="quantity">Số lượng</div>
          <div className="price">Thành tiền</div>
        </div>
        
        {orderItems.map(item => (
          <div className="order-item" key={item.id}>
            <div className="product-info">
              <div className="product-name">{item.name}</div>
              <div className="product-description">{item.description}</div>
              <div className="product-price">Đơn giá: {item.price.toLocaleString()}đ</div>
            </div>
            <div className="quantity">{item.quantity}</div>
            <div className="price">{item.total.toLocaleString()}đ</div>
          </div>
        ))}
      </div>
      
      <div className="order-totals">
        <div className="total-row">
          <span>Tạm tính</span>
          <span>{subtotal.toLocaleString()}đ</span>
        </div>
        <div className="total-row">
          <span>Phí vận chuyển</span>
          <span>{shipping.toLocaleString()}đ</span>
        </div>
        <div className="total-row">
          <span>Mã giảm giá</span>
          <span>{discount.toLocaleString()}đ</span>
        </div>
        <div className="total-row grand-total">
          <span>Tổng cộng</span>
          <span className="total-price">{total.toLocaleString()}đ</span>
        </div>
      </div>
      
      <div className="discount-code">
        <h3>Nhập mã ưu đãi</h3>
        <div className="discount-input">
          <input 
            type="text" 
            placeholder="Mã giảm giá" 
            value={discountCode}
            onChange={handleDiscountChange}
          />
          <button 
            className="apply-button"
            onClick={applyDiscount}
          >
            Áp dụng
          </button>
        </div>
        <div className="terms">
          <input
            type="checkbox"
            id="terms"
            checked={termsAccepted}
            onChange={handleTermsChange}
          />
          <label htmlFor="terms">
            Tôi đồng ý với các điều khoản <a href="/terms">chính sách giao hàng</a>
          </label>
        </div>
      </div>

      <div className="payment-section">
        <button
          className={`payment-button ${!termsAccepted ? 'disabled' : ''}`}
          onClick={handlePayment}
          disabled={!termsAccepted || isProcessingPayment}
        >
          {isProcessingPayment ? 'Đang xử lý...' : 'Xác nhận thanh toán'}
        </button>
      </div>

      <PaymentSuccessModal
        isOpen={showSuccessModal}
        onClose={closeSuccessModal}
        orderInfo={{
          orderId: 'DH' + Date.now().toString().slice(-6),
          total: total,
          paymentMethod: 'Thẻ tín dụng'
        }}
      />
    </div>
  );
};

export default OrderSummary;
