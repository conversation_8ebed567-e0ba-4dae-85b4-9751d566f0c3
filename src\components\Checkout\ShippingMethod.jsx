import React, { useState } from 'react';
import { FiHome, FiTruck } from 'react-icons/fi';
import './Checkout.css';

const ShippingMethod = () => {
  const [selectedShipping, setSelectedShipping] = useState('store');

  const handleShippingChange = (e) => {
    setSelectedShipping(e.target.value);
  };

  return (
    <div className="checkout-section">
      <h2 className="section-title">3. Phương thức giao hàng</h2>
      <div className="shipping-methods">
        <div className="shipping-method-card">
          <input 
            type="radio" 
            id="store" 
            name="shipping" 
            value="store" 
            checked={selectedShipping === 'store'}
            onChange={handleShippingChange}
          />
          <label htmlFor="store" className="shipping-card-label">
            <FiHome className="shipping-icon" />
            <span>Store</span>
          </label>
        </div>
        
        <div className="shipping-method-card">
          <input 
            type="radio" 
            id="delivery" 
            name="shipping" 
            value="delivery" 
            checked={selectedShipping === 'delivery'}
            onChange={handleShippingChange}
          />
          <label htmlFor="delivery" className="shipping-card-label">
            <FiTruck className="shipping-icon" />
            <span>Giao hàng</span>
          </label>
        </div>
      </div>
    </div>
  );
};

export default ShippingMethod;
