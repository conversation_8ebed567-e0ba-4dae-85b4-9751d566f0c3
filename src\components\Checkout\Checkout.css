.checkout-container {
  /* max-width: 1200px; */
  margin: 0 auto;
  /* padding: 2rem; */
}

.checkout-title {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  color: #ff6b6b;
}

.checkout-title svg {
  margin-right: 0.5rem;
}

.checkout-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.checkout-left {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.checkout-section {
  background-color: #fff;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-floating {
  position: relative;
  margin-bottom: 0.5rem;
}

.floating-label {
  position: absolute;
  top: -10px;
  left: 10px;
  background-color: white;
  padding: 0 5px;
  font-size: 0.8rem;
  color: #888;
  z-index: 1;
}

.form-floating input,
.form-floating select,
.form-floating textarea {
  width: 100%;
  padding: 1rem;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  background-color: white;
}

.form-floating select {
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
  padding-right: 2.5rem;
}

.full-width {
  grid-column: 1 / -1;
}

.form-floating textarea {
  min-height: 120px;
  resize: vertical;
}

/* Payment Methods */
.payment-methods {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.payment-method-card {
  position: relative;
}

.payment-method-card input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.payment-card-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #eee;
}

.payment-method-card input[type="radio"]:checked+.payment-card-label {
  border-color: #ff6b6b;
  box-shadow: 0 2px 12px rgba(255, 107, 107, 0.1);
}

.payment-logo {
  height: 30px;
  width: auto;
  object-fit: contain;
}

@media (max-width: 768px) {
  .payment-methods {
    grid-template-columns: 1fr;
  }
}

/* Shipping Methods */
.shipping-methods {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.shipping-method-card {
  position: relative;
}

.shipping-method-card input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

.shipping-card-label {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid #eee;
}

.shipping-method-card input[type="radio"]:checked+.shipping-card-label {
  border-color: #ff6b6b;
  box-shadow: 0 2px 12px rgba(255, 107, 107, 0.1);
}

.shipping-icon {
  font-size: 1.5rem;
  color: #333;
}

@media (max-width: 768px) {
  .shipping-methods {
    grid-template-columns: 1fr;
  }
}

/* Order Summary */
.order-summary {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.order-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 1rem 0;
  border-bottom: 1px solid #eee;
  font-weight: 600;
  color: #333;
}

.order-item {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  padding: 1.5rem 0;
  border-bottom: 1px solid #eee;
  align-items: center;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.product-name {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

.product-description {
  font-size: 0.85rem;
  color: #999;
  line-height: 1.4;
}

.product-price {
  font-size: 0.9rem;
  color: #666;
  margin-top: 0.25rem;
}

.quantity {
  text-align: center;
  font-weight: 500;
}

.price {
  text-align: right;
  font-weight: 600;
}

.order-totals {
  margin-top: 1.5rem;
  padding-top: 1rem;
}

.total-row {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  color: #333;
}

.grand-total {
  font-weight: 700;
  font-size: 1.1rem;
  margin-top: 0.5rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.total-price {
  color: #ff6b6b;
}

.discount-code {
  margin-top: 2rem;
}

.discount-code h3 {
  font-size: 1rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.discount-input {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.discount-input input {
  flex: 1;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ddd;
  font-size: 0.9rem;
}

.apply-button {
  padding: 0 1.5rem;
  background-color: #ffb6c1;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.apply-button:hover {
  background-color: #ff9aa2;
}

.terms {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  font-size: 0.9rem;
  color: #333;
}

.terms input[type="checkbox"] {
  margin-top: 0.25rem;
}

.terms a {
  color: #ff6b6b;
  text-decoration: none;
}

.terms a:hover {
  text-decoration: underline;
}

@media (max-width: 768px) {
  .checkout-content {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .payment-methods {
    flex-direction: column;
  }

  .order-item {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .order-header {
    display: none;
  }

  .quantity,
  .price {
    text-align: left;
    display: flex;
    justify-content: space-between;
  }

  .quantity::before {
    content: "Số lượng:";
    font-weight: 500;
  }

  .price::before {
    content: "Thành tiền:";
    font-weight: 500;
  }
}

/* Payment Button */
.payment-section {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid #eee;
}

.payment-button {
  width: 100%;
  padding: 1.2rem 2rem;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.payment-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff5252, #ff7979);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.payment-button:active:not(:disabled) {
  transform: translateY(0);
}

.payment-button.disabled,
.payment-button:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(50px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-close {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #999;
  transition: color 0.2s;
}

.modal-close:hover {
  color: #333;
}

.success-icon {
  text-align: center;
  margin-bottom: 1.5rem;
}

.success-icon svg {
  font-size: 4rem;
  color: #4caf50;
}

.success-title {
  text-align: center;
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  font-weight: 600;
}

.success-message {
  text-align: center;
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.5;
}

.order-details {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
}

.detail-row:last-child {
  border-bottom: none;
}

.order-id {
  font-weight: 600;
  color: #ff6b6b;
}

.total-amount {
  font-weight: 600;
  color: #4caf50;
  font-size: 1.1rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

.btn-primary {
  padding: 0.8rem 1.5rem;
  background: linear-gradient(135deg, #ff6b6b, #ff8e8e);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #ff5252, #ff7979);
  transform: translateY(-1px);
}

.btn-secondary {
  padding: 0.8rem 1.5rem;
  background: white;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: #f8f9fa;
  border-color: #ccc;
}

@media (max-width: 768px) {
  .modal-content {
    margin: 1rem;
    padding: 1.5rem;
  }

  .modal-actions {
    flex-direction: column;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
  }
}